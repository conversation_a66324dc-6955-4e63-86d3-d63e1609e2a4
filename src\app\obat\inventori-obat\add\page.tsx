"use client";

import { FormDatePicker } from "@/components/custom/datepicker";
import { FormInput } from "@/components/custom/input";
import { SelectSearchAsync } from "@/components/custom/selectSearchAsync";
import { Button, SelectItem } from "@heroui/react";
import { DateValue } from "@internationalized/date";
import { useFormik } from "formik";
import { Plus, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { postPenambahanObat } from "./schema/postPenambahanObat";
import { ContainerDiv } from "@/components/ui/container";

export const data = [
  {
    id: "1",
    nameObat: "Amoxilin",
    dosis: "500mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
  {
    id: "2",
    nameObat: "Amoxilin",
    dosis: "100mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
  {
    id: "3",
    nameObat: "Paracetamol",
    dosis: "1000mg",
    bentuk: "tablet",
    satuan: "tablet",
    jenis: "analgenis",
  },
];

export type listObat = {
  id: string;
  name: string;
  quantityIn: number;
  tglExpired: DateValue | null;
  perusahaan: string;
};

type payload = {
  tglMasuk: DateValue | null;
  noBatch: string;
  listObat: Array<listObat>;
};

const initialList = {
  id: `obat-${Date.now()}`,
  name: "",
  quantityIn: 0,
  tglExpired: null,
  perusahaan: "",
};

export default function PageDetailInventoriObat() {
  const route = useRouter();
  const [resultFilter, setResultFilter] = useState<(typeof data)[0] | null>(
    null
  );

  const handleSubmit = async (value: payload) => {
    alert(JSON.stringify(value));
  };

  const initialValues = {
    tglMasuk: null,
    noBatch: "",
    listObat: [initialList],
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: postPenambahanObat,
    onSubmit: handleSubmit,
  });

  // Function to add new obat item
  const addObatItem = () => {
    const newObat: listObat = initialList;

    formik.setFieldValue("listObat", [...formik.values.listObat, newObat]);
  };

  // Function to remove obat item
  const removeObatItem = (index: number) => {
    const updatedList = formik.values.listObat.filter((_, i) => i !== index);
    formik.setFieldValue("listObat", updatedList);
  };

  // Function to update specific obat item
  const updateObatItem = (index: number, field: keyof listObat, value: any) => {
    const updatedList = [...formik.values.listObat];
    updatedList[index] = { ...updatedList[index], [field]: value };
    formik.setFieldValue("listObat", updatedList);
    const result = data.find((item) => item.id === value);
    setResultFilter(result ? result : null);
  };

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState(data);
  const [isLoading, setIsLoading] = useState(false);
  const [debouncedTerm, setDebouncedTerm] = useState("");

  // Debounce the search term to avoid too many rapid API calls
  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 500); // 500ms delay

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);

  useEffect(() => {
    const timerId = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 500); // 500ms delay

    return () => {
      clearTimeout(timerId);
    };
  }, [searchTerm]);

  useEffect(() => {
    if (debouncedTerm === "") {
      setFilteredData(data);
      return;
    }

    const filterData = async () => {
      setIsLoading(true);

      // Simulate API delay (1 second)
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const filtered = data.filter(
        (item) =>
          item.nameObat.toLowerCase().includes(debouncedTerm.toLowerCase()) ||
          item.dosis.toLowerCase().includes(debouncedTerm.toLowerCase())
      );

      setFilteredData(filtered);
      setIsLoading(false);
    };

    filterData();
  }, [debouncedTerm]);

  return (
    <ContainerDiv>
      <div className="w-full flex justify-center pb-4">
        <div className="bg-default-50 flex flex-col gap-6 p-5 rounded-md lg:w-1/2 w-full ">
          {JSON.stringify(searchTerm)}
          <h1 className="text-xl font-medium mb-2">
            Form Tambah Penambahan Obat Massal
          </h1>
          <form onSubmit={formik.handleSubmit} className="flex flex-col gap-6">
            {/* Basic Information */}
            <div className="flex  gap-6">
              <div className="flex flex-col gap-1 w-full">
                <label htmlFor="tglMasuk" className="text-sm">
                  Tanggal Perolehan
                </label>
                <FormDatePicker name="tglMasuk" formik={formik} />
              </div>
              <div className="flex flex-col gap-1 w-full">
                <label htmlFor="noBatch" className="text-sm">
                  No. Batch
                </label>
                <FormInput
                  isClearable={false}
                  name={"noBatch"}
                  isNumeric={false}
                  formik={formik}
                  placeholder="Ketik No. Batch obat masuk"
                />
              </div>
            </div>
            <span className=" border-b-2 border-default-400 pb-2 " />
            {/* Dynamic List Obat Section */}
            <div className="flex flex-col gap-4">
              <div className=""></div>
              {/* List of Obat Items */}
              <div className="flex flex-col gap-4">
                {formik.values.listObat.map((obat, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg flex flex-col gap-6 p-4 bg-default-50"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium ">Obat {index + 1}</h3>
                      <Button
                        type="button"
                        color="danger"
                        variant="solid"
                        size="sm"
                        isIconOnly
                        onClick={() => removeObatItem(index)}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </div>

                    <div className="flex flex-col gap-4">
                      {/* Nama Obat */}
                      <div className="flex flex-col gap-1">
                        <label className="text-sm">Nama Obat</label>
                        <SelectSearchAsync
                          isLoadig={isLoading}
                          showSeachbar={true}
                          name={`listObat.${index}.name`}
                          formik={formik}
                          placeholder="Masukkan nama obat"
                          value={obat.name}
                          onFilter={setSearchTerm}
                          search={searchTerm}
                          isCustomizing={true}
                          onChange={(e: any) => {
                            updateObatItem(index, "name", e.target.value);
                          }}
                        >
                          {filteredData.map((item) => (
                            <SelectItem
                              key={item.id}
                              textValue={item.nameObat}
                              // Menyimpan seluruh data item sebagai prop
                            >
                              <div className="flex flex-col gap-2">
                                <p>{item.nameObat}</p>
                                <p>{item.bentuk}</p>
                                <p>{item.dosis}</p>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectSearchAsync>
                      </div>
                      {resultFilter !== null && (
                        <div className="grid grid-cols-2">
                          <div className="p-4 rounded text-default-500  bg-semantic-info-50 flex flex-col gap-2">
                            <p className="text-sm ">Nama Obat</p>
                            <p className="text-sm ">Dosis</p>
                            <p className="text-sm ">Bentuk</p>
                            <p className="text-sm ">Satuan</p>
                            <p className="text-sm ">Jenis</p>
                          </div>
                          <div className="p-4 rounded  bg-semantic-info-50 flex flex-col gap-2">
                            <p className="text-sm ">
                              : {resultFilter?.nameObat}
                            </p>
                            <p className="text-sm ">: {resultFilter?.dosis}</p>
                            <p className="text-sm ">: {resultFilter?.bentuk}</p>
                            <p className="text-sm ">: {resultFilter?.satuan}</p>
                            <p className="text-sm ">: {resultFilter?.jenis}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Quantity In */}
                    <div className="flex gap-6 ">
                      <div className="flex flex-col gap-1 w-full">
                        <label className="text-sm">Jumlah Masuk</label>
                        <FormInput
                          isClearable={false}
                          name={`listObat.${index}.quantityIn`}
                          isNumeric={true}
                          formik={formik}
                          placeholder="0"
                          value={obat.quantityIn.toString()}
                          onChange={(e) =>
                            updateObatItem(
                              index,
                              "quantityIn",
                              parseInt(e.target.value) || 0
                            )
                          }
                        />
                      </div>

                      {/* Perusahaan */}
                      <div className="flex flex-col gap-1 w-full">
                        <label className="text-sm">Perusahaan</label>
                        <FormInput
                          isClearable={false}
                          name={`listObat.${index}.perusahaan`}
                          isNumeric={false}
                          formik={formik}
                          placeholder="Nama perusahaan"
                          value={obat.perusahaan}
                          onChange={(e) =>
                            updateObatItem(index, "perusahaan", e.target.value)
                          }
                        />
                      </div>
                    </div>

                    {/* Tanggal Expired */}
                    <div className="flex flex-col gap-1 md:col-span-2 lg:col-span-1">
                      <label className="text-sm">Tanggal Expired</label>
                      <FormDatePicker
                        name={`listObat.${index}.tglExpired`}
                        formik={formik}
                        onChange={(value) =>
                          updateObatItem(index, "tglExpired", value)
                        }
                      />
                    </div>
                  </div>
                ))}
              </div>
              <div className="flex items-center justify-between">
                <Button
                  type="button"
                  color="default"
                  className="bg-success-100 font-semibold text-green-600"
                  size="sm"
                  startContent={<Plus size={16} />}
                  onPress={addObatItem}
                >
                  Tambah Obat
                </Button>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex gap-6 justify-end">
              <div className="flex justify-end pt-4">
                <Button
                  color="default"
                  className="bg-default-50 border-2 border-md"
                  size="md"
                  onPress={() => {
                    formik.resetForm();
                    route.back();
                  }}
                >
                  Kembali
                </Button>
              </div>
              <div className="flex justify-end pt-4">
                <Button
                  type="submit"
                  color="primary"
                  size="md"
                  isDisabled={formik.values.listObat.length === 0}
                >
                  Simpan
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </ContainerDiv>
  );
}
