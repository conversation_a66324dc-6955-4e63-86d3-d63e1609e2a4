"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import {
  addToast,
  Switch
} from "@heroui/react";
import { use, useCallback } from "react";
import { ResponsePoli, TypePoli } from "../../poli/_schema/typePoli";
import { patchtLab } from "../actions";
import Modals from "./modal";
import ModalPost from "./modalPost";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Laboratorium",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableLaboratorium({ lab }: { lab: any }) {
  const dataLab: ResponsePoli = use(lab);
  const result = dataLab?.content ?? []
  const pageInfo = dataLab?.page

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    try {
      const res = await patchtLab({ active: !currentSwitch, id: id });
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    }
  };

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div className="">{category.name}</div>;
        case "active":
          return (
            <div className="flex gap-2 justify-end  ">
              <Switch
                size="sm"
                onValueChange={() =>
                  handleChangeSwitch({
                    id: category.id,
                    currentSwitch: category.active,
                  })
                }
                defaultSelected={category.active}
              />
              <p className="capitalize">
                {category.active ? "aktif" : "tidak aktif"}
              </p>
            </div>
          );
        case "action":
          return <Modals id={category.id} />;
        default:
          return category[column_key];
      }
    },
    []
  );
  const props: PropsTable<TypePoli> = { columns, renderCell, data: result, pageInfo: pageInfo, basePath: "/referensi/laboratorium" }
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Laboratorium</h1>
        <ModalPost />
      </div>
      < TablePoliklinik {...props} />
    </div>
  );
}
