"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { PropsTable } from "@/interfaces/tables";
import { Chip } from "@heroui/react";
import { useCallback } from "react";
import Modals from "./modal";
import ModalPost from "./modalPost";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    tugas: ["Poli Umum", "Poli Gigi", "Poli Anak"],
    jenis: "<PERSON><PERSON><PERSON>",
  },
  {
    id: 2,
    name: "<PERSON>",
    tugas: ["<PERSON><PERSON> Umum", "Poli Gigi"],
    jenis: "Analisa Lab",
  },
  {
    id: 3,
    name: "<PERSON>",
    tugas: ["Poli Umum"],
    jenis: "Analisa Lab",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "<PERSON>a Tenaga Medis",
  },
  {
    key: "jenis",
    label: "<PERSON><PERSON>",
  },
  {
    key: "tugas",
    label: "<PERSON><PERSON><PERSON>",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

export default function TableDokter() {
  const data: any = users;
  const pageInfo = data?.page;
  const result: any[] = users;

  const renderCell = useCallback(
    (category: any, column_key: any, index: number) => {
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return <div>{category.name}</div>;
        case "tugas":
          return (
            <div className="flex flex-wrap  gap-2">
              {category.tugas.map((item: any, index: number) => (
                <Chip key={index} size="sm">
                  {item}
                </Chip>
              ))}
            </div>
          );
        case "action":
          return <Modals id={category.id} />;
        default:
          return category[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: result,
    pageInfo: pageInfo,
    basePath: "/referensi/poli",
  };

  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Dokter yang Bertugas</h1>
        <ModalPost />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
