"use client";

import { FormUpload } from "@/components/custom/input";
import { useFormik } from "formik";
import { Button } from "@heroui/react";

type FormData = {
  document: File | null;
  image: File | null;
  certificate: File | null;
  attachment: File | null;
};

export default function FileUploadExample() {
  const formik = useFormik<FormData>({
    initialValues: {
      document: null,
      image: null,
      certificate: null,
      attachment: null,
    },
    onSubmit: (values) => {
      console.log("Uploaded files:", values);
    },
  });

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">
        Contoh File Upload dengan Berbagai Style
      </h1>

      <form onSubmit={formik.handleSubmit} className="space-y-6">
        {/* Style Default */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Dokumen (Style Default)
          </label>
          <FormUpload
            name="document"
            formik={formik}
            variant="default"
            placeholder="Pilih file dokumen..."
            accept=".pdf,.doc,.docx"
          />
        </div>

        {/* Style Right Button */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Gambar (Tombol Kanan)
          </label>
          <FormUpload
            name="image"
            formik={formik}
            variant="rightButton"
            placeholder="Tidak ada gambar dipilih"
            accept="image/*"
          />
        </div>

        {/* Style Modern Right */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Sertifikat (Modern Right)
          </label>
          <FormUpload
            name="certificate"
            formik={formik}
            variant="modernRight"
            placeholder="Belum ada sertifikat dipilih"
            accept=".pdf,.jpg,.png"
          />
        </div>

        {/* Style Elegant Right */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Lampiran (Elegant Right)
          </label>
          <FormUpload
            name="attachment"
            formik={formik}
            variant="elegantRight"
            placeholder="Tidak ada lampiran dipilih"
            accept="*"
          />
        </div>

        {/* Custom Style */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Custom Style
          </label>
          <FormUpload
            name="attachment"
            formik={formik}
            customClasses={{
              label: "font-bold text-purple-700",
              input: "relative flex w-full text-sm py-4 px-4 border-2 border-purple-300 rounded-xl cursor-pointer hover:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-purple-50 hover:bg-purple-100 file:absolute file:right-3 file:top-1/2 file:-translate-y-1/2 file:px-6 file:py-2 file:border-0 file:bg-purple-600 file:text-white file:text-sm file:font-bold file:rounded-lg hover:file:bg-purple-700 file:cursor-pointer pr-32"
            }}
            placeholder="Pilih file dengan style custom..."
          />
        </div>

        <div className="flex gap-4 pt-4">
          <Button type="submit" color="primary">
            Upload Files
          </Button>
          <Button 
            type="button" 
            variant="bordered" 
            onPress={() => formik.resetForm()}
          >
            Reset
          </Button>
        </div>
      </form>

      {/* Preview uploaded files */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-700 mb-3">File yang dipilih:</h3>
        <div className="space-y-2 text-sm">
          <div>
            <span className="font-medium">Dokumen:</span> {formik.values.document?.name || "Belum dipilih"}
          </div>
          <div>
            <span className="font-medium">Gambar:</span> {formik.values.image?.name || "Belum dipilih"}
          </div>
          <div>
            <span className="font-medium">Sertifikat:</span> {formik.values.certificate?.name || "Belum dipilih"}
          </div>
          <div>
            <span className="font-medium">Lampiran:</span> {formik.values.attachment?.name || "Belum dipilih"}
          </div>
        </div>
      </div>
    </div>
  );
}
