"use client";

import TablePoliklinik from "@/components/table/tablePoliklinik";
import { addToast, Switch } from "@heroui/react";
import { use, useState } from "react";
import { ResponsePoli, TypePoli } from "../_schema/typePoli";
import { patchtPoli } from "../actions";
import Modals from "./modal";
import ModalPost from "./modalPost";
import { PropsTable } from "@/interfaces/tables";

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "active",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

type Props = {
  poli: any;
  onChangeSwitch?: (id: number) => void;
};

export default function TablePoli({ poli }: Props) {
  const data: ResponsePoli = use(poli);
  const pageInfo = data?.page;
  const result: TypePoli[] = data?.content ?? [];

  // State untuk menyimpan nilai switch yang berubah
  const [switchStates, setSwitchStates] = useState<Record<string, boolean>>({});

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    try {
      // State sudah diupdate di onValueChange, langsung panggil API
      const res = await patchtPoli({ active: currentSwitch, id: id });
      console.log("API call successful:", res);

      return res;
    } catch (error: any) {

      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    }
  };

  const renderCell = (category: any, column_key: any, index: number) => {
    switch (column_key) {
      case "no":
        return index + 1;
      case "name":
        return <div className="">{category.name}</div>;
      case "active":
        // Ambil nilai current dari state atau fallback ke category.active
        const currentActiveState = switchStates[category.id] ?? category.active;
        return (
          <div className="flex gap-2 justify-end items-center ">
            <Switch
              key={`switch-${category.id}-${currentActiveState}`} // Force re-render when state changes
              size="sm"
              defaultSelected={category.active}
              onValueChange={(isSelected) => {
                setSwitchStates((prev) => ({
                  ...prev,
                  [category.id]: isSelected,
                }));

                // Kemudian panggil API
                // handleChangeSwitch({
                //   id: category.id,
                //   currentSwitch: isSelected,
                // });
              }}
            />
            <p className="capitalize">
              {currentActiveState ? "aktif" : "tidak aktif"}
            </p>
          </div>
        );
      case "action":
        // Gunakan nilai current state untuk modal
        const modalActiveState = switchStates[category.id] ?? category.active;
        return <Modals id={category.id} active={modalActiveState} />;
      default:
        return category[column_key];
    }
  };
  const props: PropsTable<TypePoli> = {
    columns,
    renderCell,
    data: result,
    pageInfo: pageInfo,
    basePath: "/referensi/poli",
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">Daftar Poli</h1>
        <ModalPost />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
